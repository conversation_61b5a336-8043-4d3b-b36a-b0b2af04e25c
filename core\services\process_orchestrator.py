"""
Process Orchestrator Service

World-class implementation of subprocess lifecycle management with
dependency resolution, health monitoring, and resource control.
"""

import logging
import threading
import time
import signal
import os
from typing import Dict, List, Optional, Set, Any
from collections import defaultdict
from dataclasses import dataclass
import multiprocessing
from multiprocessing import Process, Queue, Pipe
from multiprocessing.connection import Connection

from interfaces.core.process_orchestration import (
    IProcessOrchestrator, IManagedProcess, ProcessManifest, ProcessStatus,
    ProcessState, ProcessType, ProcessPriority, RestartPolicy
)
from interfaces.core.services import IConfigService, IEventBus, ILifecycleService
from interfaces.ocr.services import IOCRDataConditioningService
from modules.ocr.data_types import OCRParsedData


logger = logging.getLogger(__name__)


@dataclass
class ManagedProcessImpl:
    """Implementation of a managed process wrapper."""
    manifest: ProcessManifest
    process: Optional[Process] = None
    status: ProcessStatus = None
    start_time: Optional[float] = None
    restart_count: int = 0
    last_restart_time: Optional[float] = None
    enabled: bool = True
    
    # Process-specific resources
    pipe_conn: Optional[Connection] = None  # For OCR process
    telemetry_queue: Optional[Queue] = None  # For telemetry process
    
    def __post_init__(self):
        if self.status is None:
            self.status = ProcessStatus(
                name=self.manifest.name,
                state=ProcessState.NOT_STARTED,
                pid=None,
                start_time=None,
                restart_count=0,
                last_health_check=None,
                is_healthy=True
            )


class ProcessOrchestrator(IProcessOrchestrator, ILifecycleService):
    """
    Production-grade process orchestration service.
    
    Features:
    - Dependency-ordered startup/shutdown
    - Health monitoring with configurable checks
    - Automatic restart with exponential backoff
    - Resource management (CPU affinity, priority)
    - Graceful shutdown with timeout handling
    """
    
    def __init__(self, 
                 config_service: IConfigService,
                 event_bus: Optional[IEventBus] = None):
        """
        Initialize the process orchestrator.
        
        Args:
            config_service: Configuration service
            event_bus: Optional event bus for process events
        """
        self.logger = logger
        self._config = config_service
        self._event_bus = event_bus
        
        # Process registry
        self._processes: Dict[str, ManagedProcessImpl] = {}
        self._process_lock = threading.RLock()
        
        # Health monitoring
        self._health_monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
        # SHARED RESOURCE REGISTRY
        self._shared_resources: Dict[str, Any] = {}
        self.logger.info("Initialized shared resource registry for IPC assets.")
        
        # Store DI container reference for OCR manager notification
        self._di_container = None
        
        # OCR data conditioning service reference
        self._ocr_data_conditioning_service: Optional[IOCRDataConditioningService] = None
        
        self.logger.info("ProcessOrchestrator initialized")
    
    def set_di_container(self, container):
        """Set DI container reference for service resolution."""
        self._di_container = container
    
    def set_ocr_data_conditioning_service(self, service: IOCRDataConditioningService):
        """Sets the reference to the OCR data conditioning service."""
        self._ocr_data_conditioning_service = service
        self.logger.info("ProcessOrchestrator: OCRDataConditioningService reference set.")
    
    def start(self):
        """Start the orchestrator (ILifecycleService)."""
        print("ORCHESTRATOR_DIAG: Orchestrator start() called.", flush=True)
        self.logger.info("Starting ProcessOrchestrator...")
        
        try:
            # Load and register process manifests
            from core.process_manifests import get_process_manifests
            from utils.testrade_modes import get_current_mode, TestradeMode, requires_ipc_services
            
            # Pass the raw GlobalConfig, not the ConfigService wrapper
            raw_config = self._config.get_config() if hasattr(self._config, 'get_config') else self._config
            manifests = get_process_manifests(raw_config)
            
            print(f"ORCHESTRATOR_DIAG: Loaded {len(manifests)} manifests.", flush=True)
            self.logger.info(f"Loaded {len(manifests)} process manifests")
            for manifest in manifests:
                print(f"  - Manifest: {manifest.name}", flush=True)
                self.logger.info(f"  - {manifest.name}: {manifest.process_type.value}, deps={manifest.dependencies}")
                self.register_process(manifest)
            
            # DYNAMIC PROCESS CONTROL: Disable processes based on current mode
            current_mode = get_current_mode()
            print(f"ORCHESTRATOR_DIAG: Current mode is {current_mode.value}", flush=True)
            self.logger.info(f"Current mode: {current_mode.value}")
            
            # In TANK modes, disable telemetry and potentially OCR processes
            if current_mode in [TestradeMode.TANK_SEALED, TestradeMode.TANK_BUFFERED]:
                print("ORCHESTRATOR_DIAG: TANK mode detected - disabling telemetry processes", flush=True)
                self.logger.info("TANK mode detected - disabling telemetry processes")
                self.set_process_enabled_status("TelemetryService", False)
                self.set_process_enabled_status("OCRTelemetryProcess", False)
                
                # In TANK_SEALED mode, OCR runs in pure isolation (no telemetry)
                if current_mode == TestradeMode.TANK_SEALED:
                    print("ORCHESTRATOR_DIAG: TANK_SEALED mode - OCR will run in pure isolation (no telemetry)", flush=True)
                    self.logger.info("TANK_SEALED mode - OCR will run in pure isolation mode")
            
            # Additional safety check using requires_ipc_services
            if not requires_ipc_services():
                print("ORCHESTRATOR_DIAG: IPC services not required - disabling telemetry processes", flush=True)
                self.logger.info("IPC services not required - disabling telemetry processes")
                self.set_process_enabled_status("TelemetryService", False)
                self.set_process_enabled_status("OCRTelemetryProcess", False)
            
            # Start health monitoring
            self._start_health_monitor()
            
            # Start all enabled processes
            print("ORCHESTRATOR_DIAG: Calling start_all_processes()...", flush=True)
            self.start_all_processes()
            print("ORCHESTRATOR_DIAG: start_all_processes() returned.", flush=True)
            
            self.logger.info("ProcessOrchestrator started")
        except Exception as e:
            print(f"ORCHESTRATOR_DIAG: CRITICAL ERROR in Orchestrator.start(): {e}", flush=True)
            self.logger.critical(f"CRITICAL ERROR in Orchestrator.start(): {e}", exc_info=True)
            raise
    
    def stop(self):
        """Stop the orchestrator and all processes."""
        self.logger.info("Stopping ProcessOrchestrator...")
        
        # Signal shutdown
        self._stop_event.set()
        
        # Stop all processes in reverse dependency order
        self.stop_all_processes()
        
        # Stop health monitor
        if self._health_monitor_thread and self._health_monitor_thread.is_alive():
            self._health_monitor_thread.join(timeout=2.0)
        
        self.logger.info("ProcessOrchestrator stopped")
    
    def register_process(self, manifest: ProcessManifest) -> None:
        """Register a process manifest."""
        with self._process_lock:
            if manifest.name in self._processes:
                self.logger.warning(f"Process {manifest.name} already registered, updating...")
            
            managed_process = ManagedProcessImpl(manifest=manifest)
            self._processes[manifest.name] = managed_process
            
            self.logger.info(f"Registered process: {manifest.name} "
                           f"(type={manifest.process_type.name}, "
                           f"deps={manifest.dependencies})")
    
    def set_process_enabled_status(self, name: str, is_enabled: bool):
        """
        Dynamically enables or disables a process before startup.
        This allows high-level application logic to control which processes run.
        """
        with self._process_lock:
            if name not in self._processes:
                self.logger.error(f"Attempted to set enabled status for unregistered process: {name}")
                return
            
            managed_process = self._processes[name]
            if managed_process.enabled != is_enabled:
                managed_process.enabled = is_enabled
                self.logger.info(f"Process '{name}' has been dynamically set to ENABLED = {is_enabled}")
    
    def start_process(self, name: str) -> bool:
        """Start a specific process by name."""
        with self._process_lock:
            if name not in self._processes:
                self.logger.error(f"Process {name} not registered")
                return False
            
            managed = self._processes[name]
            
            if not managed.enabled:
                self.logger.info(f"Process {name} is disabled, skipping start")
                return False
            
            if managed.process and managed.process.is_alive():
                self.logger.warning(f"Process {name} already running")
                return True
            
            # Check dependencies
            for dep_name in managed.manifest.dependencies:
                if dep_name not in self._processes:
                    self.logger.error(f"Dependency {dep_name} not registered for {name}")
                    return False
                
                dep = self._processes[dep_name]
                if not dep.process or not dep.process.is_alive():
                    self.logger.error(f"Dependency {dep_name} not running for {name}")
                    return False
            
            return self._start_process_internal(managed)
    
    def _start_process_internal(self, managed: ManagedProcessImpl) -> bool:
        """Internal method to start a process with resource setup."""
        try:
            manifest = managed.manifest
            print(f"ORCHESTRATOR_DIAG: [{manifest.name}] Inside _start_process_internal.", flush=True)
            self.logger.info(f"Starting process: {manifest.name}")
            
            # Update status
            managed.status.state = ProcessState.STARTING
            
            # Prepare process-specific resources
            args = list(manifest.args)
            
            # Special handling for processes that need shared resources
            if manifest.name == "OCRProcess":
                print(f"ORCHESTRATOR_DIAG: [{manifest.name}] Preparing resources for OCRProcess.", flush=True)
                # Create pipe for OCR communication
                parent_conn, child_conn = multiprocessing.Pipe(duplex=False)
                managed.pipe_conn = parent_conn
                args[0] = child_conn  # First arg is pipe connection
                
                # Add telemetry queue from shared resources - ONLY if telemetry is required
                from utils.testrade_modes import requires_telemetry_service
                if requires_telemetry_service():
                    telemetry_queue = self._shared_resources.get('telemetry_queue')
                    if not telemetry_queue:
                        print("ORCHESTRATOR_DIAG: FATAL - telemetry required but telemetry_queue not found!", flush=True)
                        raise RuntimeError("Cannot start OCRProcess: telemetry required but 'telemetry_queue' shared resource not found.")
                    args[1] = telemetry_queue
                    self.logger.info("Injecting shared 'telemetry_queue' into OCRProcess.")
                else:
                    # TANK mode - no telemetry required
                    args[1] = None
                    print("ORCHESTRATOR_DIAG: TANK mode - OCRProcess starting without telemetry", flush=True)
                    self.logger.info("TANK mode detected - OCRProcess starting without telemetry")
                
                # Build OCR config (args[2])
                args[2] = self._build_ocr_config()
                
            elif manifest.name == "OCRTelemetryProcess":
                print(f"ORCHESTRATOR_DIAG: [{manifest.name}] Preparing resources for OCRTelemetryProcess.", flush=True)
                # Create queue for telemetry data
                managed.telemetry_queue = Queue(maxsize=1000)
                args[0] = managed.telemetry_queue
                # Register telemetry queue in shared resources
                self._shared_resources['telemetry_queue'] = managed.telemetry_queue
                self.logger.info(f"Created and registered 'telemetry_queue' in shared resources.")
            
            print(f"ORCHESTRATOR_DIAG: [{manifest.name}] Creating Process object...", flush=True)
            # Create the process
            process = Process(
                target=manifest.target_function,
                args=tuple(args),
                kwargs=manifest.kwargs,
                name=manifest.name
            )
            
            # Set process attributes
            process.daemon = True
            
            print(f"ORCHESTRATOR_DIAG: [{manifest.name}] Calling process.start()...", flush=True)
            # Start the process
            process.start()
            print(f"ORCHESTRATOR_DIAG: [{manifest.name}] process.start() returned.", flush=True)
            managed.process = process
            
            # Wait for startup
            start_time = time.time()
            while time.time() - start_time < manifest.startup_timeout:
                if process.is_alive():
                    managed.status.state = ProcessState.RUNNING
                    managed.status.pid = process.pid
                    managed.start_time = time.time()
                    managed.status.start_time = managed.start_time
                    
                    # Apply resource requirements
                    self._apply_resource_requirements(process, manifest.resource_requirements)
                    
                    # START OF NEW/MODIFIED LOGIC
                    # If this is the OCR process, start its dedicated listener thread
                    if manifest.name == "OCRProcess" and managed.pipe_conn:
                        ocr_listener_thread = threading.Thread(
                            target=self._ocr_pipe_listener_loop,
                            args=(managed.pipe_conn,),
                            name="Orchestrator-OCRPipeListener",
                            daemon=True
                        )
                        ocr_listener_thread.start()
                        self.logger.info("Orchestrator started dedicated listener for OCR process pipe.")
                    # END OF NEW/MODIFIED LOGIC
                    
                    self.logger.info(f"Process {manifest.name} started successfully (PID: {process.pid})")
                    return True
                time.sleep(0.1)
            
            # Startup timeout
            self.logger.error(f"Process {manifest.name} failed to start within timeout")
            managed.status.state = ProcessState.FAILED
            managed.status.error_message = "Startup timeout"
            return False
            
        except Exception as e:
            print(f"ORCHESTRATOR_DIAG: [{managed.manifest.name}] CRITICAL ERROR in _start_process_internal: {e}", flush=True)
            self.logger.error(f"Failed to start process {managed.manifest.name}: {e}", exc_info=True)
            managed.status.state = ProcessState.FAILED
            managed.status.error_message = str(e)
            return False
    
    def stop_process(self, name: str, timeout: Optional[float] = None) -> bool:
        """Stop a specific process by name."""
        with self._process_lock:
            if name not in self._processes:
                self.logger.error(f"Process {name} not registered")
                return False
            
            managed = self._processes[name]
            
            if not managed.process or not managed.process.is_alive():
                self.logger.warning(f"Process {name} not running")
                managed.status.state = ProcessState.STOPPED
                return True
            
            return self._stop_process_internal(managed, timeout)
    
    def _stop_process_internal(self, managed: ManagedProcessImpl, timeout: Optional[float] = None) -> bool:
        """Internal method to stop a process gracefully."""
        try:
            manifest = managed.manifest
            process = managed.process
            
            if not process:
                return True
            
            self.logger.info(f"Stopping process: {manifest.name}")
            managed.status.state = ProcessState.STOPPING
            
            # Use manifest timeout if not specified
            if timeout is None:
                timeout = manifest.shutdown_timeout
            
            # EXPLICIT SHUTDOWN HANDSHAKE: Send shutdown command for OCR processes
            clean_shutdown_timeout = 0.0  # Initialize for scope
            if manifest.name == "OCRProcess" and managed.pipe_conn:
                try:
                    self.logger.info(f"SHUTDOWN HANDSHAKE: Sending explicit shutdown command to {manifest.name}")
                    shutdown_message = {"type": "shutdown_command", "reason": "parent_initiated_shutdown"}
                    managed.pipe_conn.send(shutdown_message)
                    
                    # Give the child process time to shutdown cleanly
                    clean_shutdown_timeout = min(5.0, timeout * 0.5)  # Up to 5 seconds or half the total timeout
                    process.join(timeout=clean_shutdown_timeout)
                    
                    if process.is_alive():
                        self.logger.warning(f"SHUTDOWN HANDSHAKE: {manifest.name} did not respond to shutdown command, proceeding with termination")
                    else:
                        self.logger.info(f"SHUTDOWN HANDSHAKE: {manifest.name} shutdown cleanly via explicit command")
                        # Cleanup resources for clean shutdown
                        if managed.pipe_conn:
                            managed.pipe_conn.close()
                            managed.pipe_conn = None
                        managed.process = None
                        managed.status.state = ProcessState.STOPPED
                        managed.status.pid = None
                        return True  # Clean shutdown successful
                        
                except Exception as e:
                    self.logger.error(f"SHUTDOWN HANDSHAKE: Error sending shutdown command to {manifest.name}: {e}")
            
            # Try graceful termination if still alive
            if process.is_alive():
                process.terminate()
                remaining_timeout = max(1.0, timeout - clean_shutdown_timeout if clean_shutdown_timeout > 0 else timeout)
                process.join(timeout=remaining_timeout)
            
            if process.is_alive():
                self.logger.warning(f"Process {manifest.name} did not stop gracefully, killing...")
                process.kill()
                process.join(timeout=2.0)
            
            # Cleanup resources
            if managed.pipe_conn:
                managed.pipe_conn.close()
                managed.pipe_conn = None
            
            managed.process = None
            managed.status.state = ProcessState.STOPPED
            managed.status.pid = None
            
            self.logger.info(f"Process {manifest.name} stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop process {manifest.name}: {e}", exc_info=True)
            return False
    
    def restart_process(self, name: str) -> bool:
        """Restart a specific process."""
        with self._process_lock:
            if name not in self._processes:
                self.logger.error(f"Process {name} not registered")
                return False
            
            managed = self._processes[name]
            
            # Stop if running
            if managed.process and managed.process.is_alive():
                if not self._stop_process_internal(managed):
                    return False
            
            # Increment restart count
            managed.restart_count += 1
            managed.last_restart_time = time.time()
            managed.status.restart_count = managed.restart_count
            
            # Apply backoff if restarting too quickly
            if managed.last_restart_time:
                time_since_restart = time.time() - managed.last_restart_time
                if time_since_restart < managed.manifest.restart_backoff_seconds:
                    wait_time = managed.manifest.restart_backoff_seconds - time_since_restart
                    self.logger.info(f"Applying restart backoff for {name}: {wait_time:.1f}s")
                    time.sleep(wait_time)
            
            # Start the process
            return self._start_process_internal(managed)
    
    def get_process_status(self, name: str) -> Optional[ProcessStatus]:
        """Get status of a specific process."""
        with self._process_lock:
            if name not in self._processes:
                return None
            return self._processes[name].status
    
    def get_all_process_status(self) -> Dict[str, ProcessStatus]:
        """Get status of all managed processes."""
        with self._process_lock:
            return {name: proc.status for name, proc in self._processes.items()}
    
    def get_shared_resource(self, resource_name: str) -> Optional[Any]:
        """Get a shared resource by name."""
        return self._shared_resources.get(resource_name)
    
    def register_shared_resource(self, resource_name: str, resource: Any) -> None:
        """Register a shared resource for inter-process communication."""
        self._shared_resources[resource_name] = resource
        self.logger.info(f"Registered shared resource: {resource_name}")
    
    def start_all_processes(self) -> bool:
        """Start all processes in dependency order."""
        print("ORCHESTRATOR_DIAG: Inside start_all_processes().", flush=True)
        self.logger.info("Starting all processes in dependency order...")
        
        # Calculate startup order
        startup_order = self._calculate_startup_order()
        print(f"ORCHESTRATOR_DIAG: Calculated startup order: {startup_order}", flush=True)
        
        success = True
        for name in startup_order:
            print(f"ORCHESTRATOR_DIAG: Attempting to start process '{name}'...", flush=True)
            if not self.start_process(name):
                print(f"ORCHESTRATOR_DIAG: FAILED to start process '{name}'.", flush=True)
                self.logger.error(f"Failed to start process {name}")
                success = False
                # Continue trying other processes
            else:
                print(f"ORCHESTRATOR_DIAG: SUCCESSFULLY started process '{name}'.", flush=True)
        
        return success
    
    def stop_all_processes(self, timeout: Optional[float] = None) -> bool:
        """Stop all processes in reverse dependency order."""
        self.logger.info("Stopping all processes in reverse dependency order...")
        
        # Calculate startup order and reverse it
        startup_order = self._calculate_startup_order()
        shutdown_order = list(reversed(startup_order))
        
        success = True
        for name in shutdown_order:
            if not self.stop_process(name, timeout):
                self.logger.error(f"Failed to stop process {name}")
                success = False
                # Continue trying other processes
        
        return success
    
    def set_process_enabled(self, name: str, enabled: bool) -> None:
        """Enable or disable a process."""
        with self._process_lock:
            if name not in self._processes:
                self.logger.error(f"Process {name} not registered")
                return
            
            self._processes[name].enabled = enabled
            self.logger.info(f"Process {name} {'enabled' if enabled else 'disabled'}")
    
    def is_process_enabled(self, name: str) -> bool:
        """Check if a process is enabled."""
        with self._process_lock:
            if name not in self._processes:
                return False
            return self._processes[name].enabled
    
    def _calculate_startup_order(self) -> List[str]:
        """Calculate process startup order based on dependencies."""
        with self._process_lock:
            # Build dependency graph
            graph = defaultdict(list)
            in_degree = defaultdict(int)
            
            for name, managed in self._processes.items():
                if not managed.enabled:
                    continue
                    
                in_degree[name] = len(managed.manifest.dependencies)
                for dep in managed.manifest.dependencies:
                    if dep in self._processes and self._processes[dep].enabled:
                        graph[dep].append(name)
            
            # Topological sort using Kahn's algorithm
            queue = [name for name, degree in in_degree.items() if degree == 0]
            result = []
            
            while queue:
                # Sort by process type (infrastructure first)
                queue.sort(key=lambda n: (
                    self._processes[n].manifest.process_type != ProcessType.INFRASTRUCTURE,
                    n
                ))
                
                node = queue.pop(0)
                result.append(node)
                
                for neighbor in graph[node]:
                    in_degree[neighbor] -= 1
                    if in_degree[neighbor] == 0:
                        queue.append(neighbor)
            
            # Check for cycles
            if len(result) != len([n for n, m in self._processes.items() if m.enabled]):
                self.logger.error("Circular dependency detected in process graph!")
                # Return what we have
            
            return result
    
    def _start_health_monitor(self):
        """Start the health monitoring thread."""
        self._health_monitor_thread = threading.Thread(
            target=self._health_monitor_loop,
            name="ProcessHealthMonitor",
            daemon=True
        )
        self._health_monitor_thread.start()
        self.logger.info("Started process health monitor")
    
    def _health_monitor_loop(self):
        """Main health monitoring loop."""
        while not self._stop_event.is_set():
            try:
                with self._process_lock:
                    for name, managed in list(self._processes.items()):
                        if not managed.enabled:
                            continue
                        
                        # Check if process should be running
                        if managed.status.state == ProcessState.RUNNING:
                            # Check if process is alive
                            if not managed.process or not managed.process.is_alive():
                                self.logger.error(f"Process {name} died unexpectedly")
                                managed.status.state = ProcessState.FAILED
                                managed.status.error_message = "Process died"
                                
                                # Handle restart policy
                                self._handle_process_failure(managed)
                            
                            # Run health check if configured
                            elif managed.manifest.health_check:
                                # TODO: Implement health check execution
                                pass
                
                # Sleep with interruptible wait
                self._stop_event.wait(timeout=5.0)
                
            except Exception as e:
                self.logger.error(f"Error in health monitor loop: {e}", exc_info=True)
    
    def _handle_process_failure(self, managed: ManagedProcessImpl):
        """Handle process failure based on restart policy."""
        manifest = managed.manifest
        
        if manifest.restart_policy == RestartPolicy.NEVER:
            return
        
        if manifest.restart_policy == RestartPolicy.ON_FAILURE or \
           manifest.restart_policy == RestartPolicy.ALWAYS:
            # Check restart attempts
            if managed.restart_count >= manifest.max_restart_attempts:
                self.logger.error(f"Process {manifest.name} exceeded max restart attempts")
                return
            
            # Schedule restart
            self.logger.info(f"Scheduling restart for {manifest.name} "
                           f"(attempt {managed.restart_count + 1}/{manifest.max_restart_attempts})")
            
            # Use a separate thread to avoid blocking health monitor
            threading.Thread(
                target=lambda: self.restart_process(manifest.name),
                daemon=True
            ).start()
    
    def _apply_resource_requirements(self, process: Process, requirements):
        """Apply resource requirements to a process."""
        if not requirements:
            return
        
        try:
            # CPU affinity (platform-specific)
            if requirements.cpu_affinity and hasattr(process, 'cpu_affinity'):
                process.cpu_affinity = requirements.cpu_affinity
                self.logger.info(f"Set CPU affinity to {requirements.cpu_affinity}")
            
            # Priority (platform-specific)
            # TODO: Implement priority setting based on platform
            
        except Exception as e:
            self.logger.warning(f"Failed to apply resource requirements: {e}")
    
    def _build_ocr_config(self):
        """Build OCR configuration object."""
        # Import here to avoid circular dependency
        from interfaces.ocr.services import OcrConfigData
        
        # Convert config attributes to OcrConfigData
        # Use raw GlobalConfig, not the ConfigService wrapper
        config = self._config.get_config() if hasattr(self._config, 'get_config') else self._config
        
        flicker_params = getattr(config, 'flicker_filter', None)
        if flicker_params and hasattr(flicker_params, '__dict__'):
            flicker_params = flicker_params.__dict__
        
        return OcrConfigData(
            tesseract_cmd=getattr(config, 'TESSERACT_CMD', ''),
            initial_roi=getattr(config, 'ROI_COORDINATES', [0, 0, 800, 600]),
            include_all_symbols=getattr(config, 'include_all_symbols', False),
            flicker_params=flicker_params,
            debug_ocr_handler=getattr(config, 'enable_ocr_debug_logging', False),
            video_file_path=getattr(config, 'OCR_INPUT_VIDEO_FILE_PATH', None),
            video_loop_enabled=getattr(config, 'VIDEO_LOOP_ENABLED', False),
            ocr_capture_interval_seconds=getattr(config, 'ocr_capture_interval_seconds', 0.2),
            ocr_ipc_command_pull_address=getattr(config, 'ocr_ipc_command_pull_address', 'tcp://127.0.0.1:5559'),
            # Add telemetry flags from control.json
            enable_intellisense_logging=getattr(config, 'enable_intellisense_logging', False),
            enable_ipc_data_dump=getattr(config, 'ENABLE_IPC_DATA_DUMP', False),
            # Add all preprocessing parameters...
            upscale_factor=getattr(config, 'ocr_upscale_factor', 2.5),
            force_black_text_on_white=getattr(config, 'ocr_force_black_text_on_white', True),
            unsharp_strength=getattr(config, 'ocr_unsharp_strength', 1.8),
            threshold_block_size=getattr(config, 'ocr_threshold_block_size', 25),
            threshold_c=getattr(config, 'ocr_threshold_c', -3),
            red_boost=getattr(config, 'ocr_red_boost', 1.8),
            green_boost=getattr(config, 'ocr_green_boost', 1.8),
            apply_text_mask_cleaning=getattr(config, 'ocr_apply_text_mask_cleaning', True),
            text_mask_min_contour_area=getattr(config, 'ocr_text_mask_min_contour_area', 10),
            text_mask_min_width=getattr(config, 'ocr_text_mask_min_width', 2),
            text_mask_min_height=getattr(config, 'ocr_text_mask_min_height', 2),
            enhance_small_symbols=getattr(config, 'ocr_enhance_small_symbols', True),
            symbol_max_height_for_enhancement_upscaled=getattr(config, 'ocr_symbol_max_height', 20),
            period_comma_aspect_ratio_range_upscaled=(
                getattr(config, 'ocr_period_comma_ratio_min', 0.5),
                getattr(config, 'ocr_period_comma_ratio_max', 1.8)
            ),
            period_comma_draw_radius_upscaled=getattr(config, 'ocr_period_comma_radius', 3),
            hyphen_like_min_aspect_ratio_upscaled=getattr(config, 'ocr_hyphen_min_ratio', 1.8),
            hyphen_like_draw_min_height_upscaled=getattr(config, 'ocr_hyphen_min_height', 2)
        )
    
    def _ocr_pipe_listener_loop(self, pipe_conn: Connection):
        """BLACK BOX: Listens for raw bytes from the OCR subprocess pipe."""
        self.logger.info("BLACK BOX OCR pipe listener loop started.")
        print("[BLACK BOX PARENT] TRACER 6: Orchestrator's BLACK BOX pipe listener thread is ALIVE and waiting.", flush=True)
        while not self._stop_event.is_set():
            try:
                if pipe_conn.poll(timeout=0.2):
                    print("[BLACK BOX PARENT] TRACER 7: pipe.poll() is True. Raw bytes available.", flush=True)
                    # All messages now come as raw bytes - both log messages and data
                    raw_bytes = pipe_conn.recv_bytes()
                    print("[BLACK BOX PARENT] TRACER 8: pipe.recv_bytes() successful. About to parse JSON.", flush=True)
                    self._handle_ocr_raw_bytes(raw_bytes)
            except (EOFError, BrokenPipeError):
                print("[BLACK BOX PARENT] TRACER FAIL: Pipe to child process is broken.", flush=True)
                self.logger.warning("OCR process pipe closed unexpectedly. Listener stopping.")
                break
            except Exception as e:
                print(f"[BLACK BOX PARENT] TRACER FAIL: CRITICAL error in listener loop: {e}", flush=True)
                self.logger.error(f"Critical error in OCR pipe listener: {e}", exc_info=True)
                break
        print("[BLACK BOX PARENT] TRACER: Orchestrator's BLACK BOX pipe listener thread has EXITED.", flush=True)
        self.logger.info("BLACK BOX OCR pipe listener loop stopped.")

    def _handle_ocr_raw_bytes(self, raw_bytes: bytes):
        """BLACK BOX: Handles raw bytes received from the OCR subprocess."""
        try:
            print("[BLACK BOX PARENT] TRACER 9: Inside _handle_ocr_raw_bytes.", flush=True)

            # Parse the JSON string from raw bytes
            json_string = raw_bytes.decode('utf-8')
            import json
            message = json.loads(json_string)

            message_type = message.get("type")

            # Handle child process log messages
            if message_type == "child_process_log":
                log_data = message.get("data", {})
                prefix = log_data.get("prefix", "CHILD")
                log_message = log_data.get("message", "")
                # Print the child process log to the parent's console
                print(f"[{prefix}] {log_message}", flush=True)
                return

            elif message_type == "ocr_trading_signal":
                if not self._ocr_data_conditioning_service:
                    print("[BLACK BOX PARENT] TRACER FAIL: Received signal but conditioning service is NOT AVAILABLE.", flush=True)
                    self.logger.error("Received OCR trading signal but conditioning service is not set.")
                    return

                print("[BLACK BOX PARENT] TRACER 10: Message is a trading_signal. About to enqueue to conditioner.", flush=True)
                trading_data = message.get("data", {})

                # Reconstruct the minimal OCRParsedData object needed by the conditioner
                ocr_data = OCRParsedData(
                    metadata={'correlationId': trading_data.get('correlation_id'), 'eventId': trading_data.get('correlation_id')},
                    full_raw_text=trading_data.get('full_raw_text', ''),
                    overall_confidence=trading_data.get('overall_confidence', 0.0),
                    T0_ImageIngress_ns=trading_data.get('t0_mss_ns'),
                    T3_FormattedOutput_ns=trading_data.get('t3_ocr_end_ns'),
                    frame_timestamp=trading_data.get('frame_timestamp', 0.0)
                )
                # The conditioner expects this attribute to be set
                ocr_data.master_correlation_id = trading_data.get('correlation_id')
                
                # CAPTURE T5 (handoff to conditioner)
                ocr_data.t4_handoff_to_conditioner_ns = time.perf_counter_ns()

                self._ocr_data_conditioning_service.enqueue_raw_ocr_data(ocr_data)
                print("[BLACK BOX PARENT] TRACER 11: SUCCESSFULLY enqueued data to conditioning service.", flush=True)

            elif message_type == "ocr_error_event":
                self.logger.warning(f"Received error from OCR process: {message.get('data')}")
            else:
                self.logger.debug(f"Received unhandled message type from OCR process: {message_type}")
        except Exception as e:
            print(f"[BLACK BOX PARENT] TRACER FAIL: Error handling raw bytes: {e}", flush=True)
            self.logger.error(f"Error handling OCR raw bytes in orchestrator: {e}", exc_info=True)
    
    def _handle_ocr_message(self, message: Dict[str, Any]):
        """LEGACY: Routes messages received from the OCR subprocess (DEPRECATED)."""
        # This method is kept for backward compatibility but should not be used
        # with the BLACK BOX architecture
        self.logger.warning("DEPRECATED: _handle_ocr_message called instead of _handle_ocr_raw_bytes")
    
    @property
    def is_ready(self) -> bool:
        """Check if orchestrator is ready."""
        return not self._stop_event.is_set()