"""
OCR Process Main Module - Ring Buffer Architecture

This module implements deadlock-free C++/Python communication using a 
lock-free ring buffer to eliminate the fatal callback architecture.

Architecture:
- C++ Worker Thread: Writes OCR results to SPSC ring buffer (lock-free)
- Python Polling Thread: Reads from ring buffer and handles data flow
- Main Thread: Passive wait state, no GIL interference
- Zero callbacks, zero deadlocks
"""

import logging
import os
import threading
import time
import signal
import tracemalloc
from multiprocessing.connection import Connection
from typing import Dict, Any, Optional
from collections import deque
import copy
from datetime import datetime
from queue import Full

# Early logger setup for import errors
early_logger = logging.getLogger(__name__)

# --- FIX: Import all C++ extensions once in the main thread ---
try:
    # Import C++ extensions at module level for thread safety
    import accelerator_sharedmem as ocr_accelerator
except ImportError as e:
    # Handle case where this is run without C++ modules available
    early_logger.critical(f"Failed to pre-import ocr_accelerator: {e}")
    ocr_accelerator = None

# Note: telemetry_encoder is imported in telemetry_process_main.py
# OCR process doesn't need it directly
# --- END FIX ---

# OCR imports - WITH LOUD FAILURE DIAGNOSTICS
try:
    print("DIAGNOSTIC: About to import OCRService...")
    from .ocr_service import OCRService
    print("DIAGNOSTIC: OCRService imported successfully")
except Exception as e:
    print("=" * 80)
    print("CRITICAL: OCRService IMPORT FAILED IN ocr_process_main.py")
    print("=" * 80)
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
    print("=" * 80)
    raise

from interfaces.ocr.services import OcrConfigData
from .data_types import OCRParsedData, OCRSnapshot

# BLACK BOX RECORDER
ocr_crash_logger = logging.getLogger("OCR_CRASH_RECORDER")
crash_handler = logging.FileHandler("ocr_process_crash.log", mode='w')
crash_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
crash_handler.setFormatter(crash_formatter)
ocr_crash_logger.addHandler(crash_handler)
ocr_crash_logger.setLevel(logging.INFO)

# MEMORY LEAK INVESTIGATION LOGGER
memory_leak_logger = logging.getLogger("MEMORY_LEAK_ANALYZER")
os.makedirs("memleak", exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
memory_handler = logging.FileHandler(f"memleak/ocr_memory_analysis_{timestamp}.log", mode='w')
memory_formatter = logging.Formatter('%(asctime)s - PID:%(process)d - %(levelname)s - %(message)s')
memory_handler.setFormatter(memory_formatter)
memory_leak_logger.addHandler(memory_handler)
memory_leak_logger.setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)


# Ring buffer removed - telemetry handled by separate process via Queue


def setup_ocr_process_logging(log_base_dir: str, process_name: Optional[str] = None):
    """Set up logging for the OCR process."""
    if process_name is None:
        process_name = f"OCRProcessPID-{os.getpid()}"

    proc_root_logger = logging.getLogger()
    for handler in proc_root_logger.handlers[:]:
        proc_root_logger.removeHandler(handler)

    proc_root_logger.setLevel(logging.INFO)

    formatter = logging.Formatter(
        '[%(asctime)s] [OCR-%(process)d] [%(levelname)s] %(name)s: %(message)s',
        datefmt='%H:%M:%S'
    )

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    proc_root_logger.addHandler(console_handler)

    logger.info(f"OCR Process logging initialized for {process_name}")


# Telemetry scavenger thread removed - telemetry handled by separate process


class RingBufferProcessObserver:
    """
    Minimal observer for the ring buffer architecture. 
    Only provides status updates and holds shared resources.
    """
    def __init__(self, pipe_to_parent: Connection, telemetry_queue: Any):
        self.pipe_to_parent = pipe_to_parent
        self.telemetry_queue = telemetry_queue
        self.stop_event = threading.Event()

    def on_status_update(self, message: str):
        logger.info(f"[DIAGNOSTIC] OCRService status: '{message}'")


def run_ocr_service_process(
    child_pipe_conn: Connection,
    shared_output_queue: Any,   # Shared manager.Queue for output (telemetry)
    ocr_config: OcrConfigData,
    log_base_dir: str,
    config: Any = None
):
    """
    OCR Process - Zero-Copy Architecture
    The hot path has zero synchronization with telemetry.

    CRITICAL: This process uses a C++ extension that creates worker threads
    and calls Python callbacks. Special care is needed for GIL management.
    """
    ocr_crash_logger.info(f"OCR Process Started. PID: {os.getpid()}")

    # --- CHILD PROCESS OUTPUT REDIRECTION FIX ---
    import sys
    import json

    # Create a simple class that redirects writes to the pipe using the same protocol as data
    class PipeLogger:
        def __init__(self, pipe, prefix):
            self.pipe = pipe
            self.prefix = prefix
        def write(self, message):
            try:
                if message.strip():  # Only send non-empty messages
                    # Create a special log message in JSON format, sent as bytes like normal data
                    log_message = {
                        "type": "child_process_log",
                        "data": {
                            "prefix": self.prefix,
                            "message": message.strip()
                        }
                    }
                    log_bytes = json.dumps(log_message).encode('utf-8')
                    self.pipe.send_bytes(log_bytes)
            except Exception:
                # Pipe might be closed, just ignore
                pass
        def flush(self):
            pass # Pipes flush automatically

    # Redirect stdout and stderr to the pipe
    sys.stdout = PipeLogger(child_pipe_conn, "STDOUT")
    sys.stderr = PipeLogger(child_pipe_conn, "STDERR")
    # --- END OF OUTPUT REDIRECTION FIX ---

    # Set thread support for better compatibility with C++ threading
    if hasattr(sys, 'setswitchinterval'):
        # Increase the GIL switch interval to reduce conflicts
        # Default is 0.005 (5ms), we set it higher
        sys.setswitchinterval(0.05)  # 50ms
    
    # MEMORY LEAK INVESTIGATION: Initialize tracemalloc
    tracemalloc.start()
    memory_leak_logger.info("STARTUP: Memory tracking initialized with tracemalloc")
    logger.info("Memory tracking initialized with tracemalloc")
    baseline_snapshot = tracemalloc.take_snapshot()
    memory_check_counter = 0
    
    # Log initial memory state
    initial_usage = tracemalloc.get_traced_memory()
    memory_leak_logger.info(f"BASELINE: Initial memory usage - Current={initial_usage[0]/1024/1024:.1f}MB, Peak={initial_usage[1]/1024/1024:.1f}MB")
    
    try:
        # ENTERPRISE TESSDATA CONFIGURATION
        # The C++ accelerator_unified module is self-aware and finds tessdata automatically
        # using GetModuleHandleA to locate files in the same directory as the .pyd
        # No need to set TESSDATA_PREFIX - it would override the self-aware logic!
        logger.info("Enterprise: Using self-aware tessdata resolution in C++ module")
        
        # DIAGNOSTIC: Log child process environment
        logger.info(f"Child process PID: {os.getpid()}")
        logger.info(f"Python executable: {sys.executable}")
        logger.info(f"Python path: {sys.path[:3]}")
        logger.info(f"Current working directory: {os.getcwd()}")
        
        process_name = f"OCRChildProcess-{os.getpid()}"
        setup_ocr_process_logging(log_base_dir, process_name)
        logger.info("OCR Service Process - Zero-Copy Architecture")
    
        stop_event = threading.Event()
        
        # TEMPORAL LOOP BREAK: Use shared queues for cross-process communication
        if shared_output_queue:
            logger.info("Enterprise: Shared queues received - temporal causality loop broken")
        else:
            logger.info("No shared queues - running in isolated mode")
        
        # Create minimal observer for ring buffer architecture
        observer = RingBufferProcessObserver(child_pipe_conn, shared_output_queue)
        
        # --- BLACK BOX ARCHITECTURE: Create dual ring buffers for C++/Python communication ---
        # Import the C++ accelerator using the same path setup as OCRService
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        build_dir = os.path.join(project_root, "ocr_accelerator", "bin")
        if build_dir not in sys.path:
            sys.path.insert(0, build_dir)
        import accelerator_sharedmem as ocr_accelerator
        
        # Create metadata queues for shared memory architecture
        input_ring_buffer = ocr_accelerator.MetadataQueue(16)     # Input: Frame metadata only
        output_ring_buffer = ocr_accelerator.ResultRingBuffer(32) # Output: OCR results
        logger.info("Created Shared Memory queues: MetadataQueue(16) + ResultRingBuffer(32)")
        
        # --- BLACK BOX: Pass dual ring buffers to the service ---
        ocr_service_instance = OCRService(
            observer=observer,
            config=ocr_config,
            output_queue=shared_output_queue, # Still needed for telemetry path
            input_buffer=input_ring_buffer,   # BLACK BOX: Input ring buffer
            output_buffer=output_ring_buffer  # BLACK BOX: Output ring buffer
        )
        
        # Log the ROI being used from control.json
        if hasattr(ocr_config, 'initial_roi') and ocr_config.initial_roi:
            x1, y1, x2, y2 = ocr_config.initial_roi
            logger.info(f"OCR Process using ROI from control.json: x={x1}, y={y1}, width={x2-x1}, height={y2-y1}")
        else:
            logger.warning("No ROI configuration found in ocr_config")
        
        # Signal handlers
        def shutdown_handler(signum, frame):
            logger.info(f"Received signal {signum}")
            observer.stop_event.set()
        
        signal.signal(signal.SIGTERM, shutdown_handler)
        signal.signal(signal.SIGINT, shutdown_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, shutdown_handler)
        
        # --- BLACK BOX DUMB RELAY THREAD ---
        def black_box_relay_thread():
            """
            THE DUMB RELAY: Polls C++ output buffer for pre-serialized JSON.
            Sends raw bytes directly to parent process pipe.
            NO JSON parsing. NO dictionary creation. NO thinking.
            """
            logger.info("BLACK BOX Dumb Relay thread started")
            frame_counter = 0
            
            while not observer.stop_event.is_set():
                try:
                    # Read pre-serialized result from C++ output buffer
                    result_package = output_ring_buffer.try_read()
                    
                    if result_package is not None:  # Got pre-pickled payload from C++
                        frame_counter += 1
                        
                        # --- TRACER BULLET 1 ---
                        print(f"[BLACK BOX CHILD] TRACER 1: C++ produced result. Frame: {frame_counter}", flush=True)
                        
                        # The C++ already provides a pre-serialized JSON string!
                        # Just grab it directly and encode to bytes
                        import json
                        payload_bytes = result_package.json_payload.encode('utf-8')
                        
                        # Parse it back to dict for telemetry path (we need individual fields)
                        payload_dict = json.loads(result_package.json_payload)
                        
                        # --- TRACER BULLET 2 ---
                        print(f"[BLACK BOX CHILD] TRACER 2: About to send raw bytes over pipe. Frame: {frame_counter}", flush=True)
                        
                        # Send raw bytes. This bypasses Python's dictionary pickling.
                        try:
                            observer.pipe_to_parent.send_bytes(payload_bytes)
                            print(f"[BLACK BOX CHILD] TRACER 3: SUCCESSFULLY sent raw bytes over pipe. Frame: {frame_counter}", flush=True)
                        except (BrokenPipeError, EOFError):
                            print("[BLACK BOX CHILD] TRACER FAIL: Pipe to parent is broken. Relay shutting down.", flush=True)
                            observer.stop_event.set()
                            break
                        
                        # --- COLD PATH: Send to telemetry if available ---
                        if observer.telemetry_queue:
                            telemetry_data = {
                                'json_payload': result_package.json_payload,  # Already a JSON string from C++
                                't0_ns': result_package.t0_ns,
                                't4_ns': result_package.t4_ns,
                                'confidence': result_package.confidence,
                                'frame_timestamp': time.time()
                            }
                            try:
                                print(f"[BLACK BOX CHILD] TRACER 4: About to put telemetry data. Frame: {frame_counter}", flush=True)
                                observer.telemetry_queue.put_nowait(telemetry_data)
                                print(f"[BLACK BOX CHILD] TRACER 5: SUCCESSFULLY put telemetry data. Frame: {frame_counter}", flush=True)
                            except Full:
                                print(f"[BLACK BOX CHILD] TRACER FAIL: Telemetry queue full. Frame: {frame_counter}", flush=True)
                                logger.warning(f"Telemetry queue full. Dropping data for frame {frame_counter}.")
                        
                        if frame_counter % 50 == 0:
                            logger.info(f"BLACK BOX Relay processed frame {frame_counter}")
                    
                    else:
                        # No data available, sleep briefly to avoid busy waiting
                        time.sleep(0.001)  # 1ms sleep
                        
                except Exception as e:
                    print(f"[BLACK BOX CHILD] TRACER FAIL: ERROR in relay thread: {e}", flush=True)
                    logger.error(f"Error in BLACK BOX relay thread: {e}", exc_info=True)
                    observer.stop_event.set()
                    break
            
            logger.info("BLACK BOX Dumb Relay thread stopped")
        
        # Start the BLACK BOX relay thread
        relay_thread = threading.Thread(target=black_box_relay_thread, name="BlackBoxRelay", daemon=True)
        relay_thread.start()
        logger.info("BLACK BOX Dumb Relay thread started")
        
        # Start OCR processing
        logger.info("Starting C++ OCR processing loop...")
        logger.info(f"Observer type: {type(observer)}")
        logger.info(f"Observer has telemetry_queue: {hasattr(observer, 'telemetry_queue')}")
        logger.info(f"Telemetry queue: {observer.telemetry_queue if hasattr(observer, 'telemetry_queue') else 'None'}")
        
        # Start the OCR service - this creates the C++ worker thread
        ocr_service_instance.start_ocr()
        logger.info("C++ OCR processing loop started - worker thread should be running")
        
        # CRITICAL: The C++ code creates a worker thread that needs to acquire
        # the GIL to call Python callbacks. We must ensure our main thread
        # doesn't interfere with this process.
        
        logger.info("Main thread waiting for shutdown signal...")
        
        # Debug counter to track main loop activity
        loop_count = 0
        
        # CRITICAL: The C++ worker thread is now running and will call Python callbacks
        # We must keep this thread alive without interfering with GIL management
        
        # =======================================================================
        # === EXPLICIT SHUTDOWN HANDSHAKE: Active Polling for Parent Commands ===
        # =======================================================================
        # The main thread now actively polls for shutdown commands from the parent
        # while allowing the C++ thread to run. This ensures clean, orderly shutdown.
        
        try:
            logger.info("Main thread waiting for shutdown signal.")
            
            # The only job of this main thread is to keep the process alive
            # by waiting on the observer's stop event. The signal handler
            # will set this event on SIGTERM/SIGINT.
            observer.stop_event.wait()  # Blocking wait - zero CPU usage
            
            logger.info("Main thread received stop signal.")
        
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received by main thread. Initiating shutdown.")
            observer.stop_event.set()
        
        except Exception as e:
            logger.error(f"Error in main thread polling loop: {e}", exc_info=True)
            observer.stop_event.set()
            
        # =======================================================================
        
        logger.info("SHUTDOWN SEQUENCE: Stop event received, initiating clean shutdown")
        
        # EXPLICIT SHUTDOWN SEQUENCE: Ensure all threads join cleanly
        if 'relay_thread' in locals() and relay_thread.is_alive():
            logger.info("SHUTDOWN SEQUENCE: Waiting for BLACK BOX relay thread to complete...")
            relay_thread.join(timeout=2.0)  # Give relay thread time to finish
            if relay_thread.is_alive():
                logger.warning("SHUTDOWN SEQUENCE: Relay thread did not exit cleanly")
            else:
                logger.info("SHUTDOWN SEQUENCE: Relay thread completed successfully")
            
    except Exception as e:
        ocr_crash_logger.critical(f"FATAL: {e}", exc_info=True)
        try:
            error_msg = {
                "type": "ocr_error_event",
                "data": {"error": str(e)}
            }
            child_pipe_conn.send(error_msg)
        except:
            pass
    
    finally:
        logger.info("SHUTDOWN SEQUENCE: OCR process entering final cleanup...")
        
        # Ensure stop_event is set for all threads
        if 'observer' in locals():
            observer.stop_event.set()
        if 'stop_event' in locals():
            stop_event.set()
        
        # SHUTDOWN STEP 1: Stop OCR service (this calls C++ stop() and joins C++ worker thread)
        if 'ocr_service_instance' in locals():
            try:
                logger.info("SHUTDOWN SEQUENCE: Calling OCR service shutdown...")
                ocr_service_instance.shutdown()
                logger.info("SHUTDOWN SEQUENCE: OCR service shutdown completed")
            except Exception as e:
                logger.error(f"SHUTDOWN SEQUENCE: Error stopping OCR service: {e}")
        
        # SHUTDOWN STEP 2: Ensure relay thread is joined (if not already done)
        if 'relay_thread' in locals() and relay_thread.is_alive():
            logger.info("SHUTDOWN SEQUENCE: Final join attempt for relay thread...")
            relay_thread.join(timeout=1.0)
            if relay_thread.is_alive():
                logger.error("SHUTDOWN SEQUENCE: Relay thread failed to exit - process may have resource leaks")
            else:
                logger.info("SHUTDOWN SEQUENCE: Relay thread joined successfully")
        
        # No telemetry thread to join - handled by separate process
        
        # MEMORY LEAK INVESTIGATION: Final memory report and cleanup
        try:
            if tracemalloc.is_tracing():
                final_usage = tracemalloc.get_traced_memory()
                final_msg = f"FINAL: Current={final_usage[0]/1024/1024:.1f}MB, Peak={final_usage[1]/1024/1024:.1f}MB"
                logger.info(f"MEMORY_{final_msg}")
                memory_leak_logger.info(final_msg)
                
                # Take final snapshot comparison
                final_snapshot = tracemalloc.take_snapshot()
                final_stats = final_snapshot.compare_to(baseline_snapshot, 'lineno')
                
                memory_leak_logger.info("SHUTDOWN: Final memory growth analysis:")
                for index, stat in enumerate(final_stats[:20]):  # Top 20 for final analysis
                    if stat.size_diff > 1024:  # > 1KB growth for final detailed report
                        memory_leak_logger.info(f"FINAL_GROWTH[{index}]: {stat.traceback.format()[-1].strip()} grew by {stat.size_diff/1024:.1f}KB (total: {stat.size/1024/1024:.1f}MB)")
                
                tracemalloc.stop()
                memory_leak_logger.info("SHUTDOWN: Memory tracking stopped")
                logger.info("Memory tracking stopped")
        except Exception as e:
            error_msg = f"Error stopping memory tracking: {e}"
            logger.error(error_msg)
            memory_leak_logger.error(error_msg)
        
        logger.info("OCR process shutdown complete")
